/* إعدادات عامة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Cairo', sans-serif;
}

:root {
    --main-bg-color: #121212;
    --secondary-bg-color: #1e1e1e;
    --text-color: #ffffff;
    --accent-color: #e91e63;
    --accent-hover: #c2185b;
    --card-bg-color: #2c2c2c;
    --border-color: #3d3d3d;
    --modal-bg-color: #1e1e1e;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --font-size-base: 1.1rem; /* إضافة حجم خط أساسي أكبر */
}

html, body {
    height: 100%;
    background-color: var(--main-bg-color);
    color: var(--text-color);
    direction: rtl;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* رأس التطبيق */
#app-header {
    background-color: var(--secondary-bg-color);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

#app-header.hidden {
    transform: translateY(-100%);
}

.logo h1 {
    font-size: 2rem; /* كان 1.8rem */
    color: var(--accent-color);
}

.search-container {
    display: flex;
    flex: 1;
    max-width: 700px;
    margin: 0 1rem;
}

#search-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    font-size: 1.3rem; /* تكبير حجم الخط من 1.1rem إلى 1.3rem */
}

#search-app-btn, #search-google-btn, #search-yandex-btn {
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    background-color: var(--accent-color);
    color: white;
    transition: background-color 0.3s;
    font-size: 1.3rem; /* تكبير حجم الخط من 1.2rem إلى 1.3rem */
}

#search-app-btn {
    border-radius: 0;
}

#search-google-btn {
    background-color: #4285F4;
}

#search-yandex-btn {
    background-color: #FF0000;
    border-radius: 0 4px 4px 0;
}

#search-app-btn:hover, #search-google-btn:hover, #search-yandex-btn:hover {
    opacity: 0.9;
}

.settings-btn button {
    background-color: var(--secondary-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.settings-btn button:hover {
    background-color: var(--card-bg-color);
}

/* قسم الأقسام */
.categories-section {
    background-color: var(--secondary-bg-color);
    padding: 1rem;
    transition: max-height 0.5s ease, padding 0.5s ease;
    max-height: 50vh;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-color);
    margin-top: 70px;
}

.categories-section.collapsed {
    max-height: 0;
    padding: 0 1rem;
    overflow: hidden;
}

.categories-section h2 {
    font-size: 2rem; /* كان 1.8rem */
    margin-bottom: 0.8rem;
    color: var(--accent-color);
}

.categories-section ul {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.categories-section li {
    font-size: 1.4rem; /* كان 1.3rem */
    padding: 0.6rem 1.2rem;
    background-color: var(--card-bg-color);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.categories-section li:hover {
    background-color: var(--accent-color);
}

.categories-section li.active {
    background-color: var(--accent-color);
}

/* تحسين مظهر عداد الأفلام بجوار أسماء الأقسام */
.counter {
    background-color: var(--main-bg-color);
    color: var(--accent-color);
    border-radius: 12px; /* تغيير من 50% إلى 12px للحصول على مستطيل بحواف دائرية */
    min-width: 40px; /* إضافة عرض أدنى ليتسع للأرقام الكبيرة */
    height: 32px; /* الحفاظ على الارتفاع */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 0.8rem;
    font-weight: bold;
    border: 1px solid var(--accent-color);
    margin-left: 8px;
    padding: 0 8px; /* إضافة حشو جانبي ليتسع للأرقام الكبيرة */
}

.categories-section li.active .counter {
    background-color: white;
    color: var(--accent-color);
}

.categories-section li:hover .counter {
    background-color: white;
    color: var(--accent-color);
}

.main-categories, .sub-categories, .special-categories, .special-sub-categories {
    margin-bottom: 1rem;
}

/* أزرار خيارات الأقسام الفرعية */
.subcategory-options {
    margin: 15px 0;
}

.options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.subcategory-option {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}

.subcategory-option:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
}

.subcategory-option.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* منطقة السحب والإفلات */
.dropzone-section {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--secondary-bg-color);
}

.dropzone {
    flex: 1;
    border: 2px dashed var(--border-color);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    transition: border-color 0.3s;
}

.dropzone.dragover {
    border-color: var(--accent-color);
}

.dropzone h3 {
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.dropzone input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    margin: 1rem 0;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
}

.dropzone button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dropzone button:hover {
    background-color: var(--accent-hover);
}

/* محتوى الأفلام الرئيسي */
#content-section {
    flex: 1;
    padding: 1rem;
    background-color: var(--main-bg-color);
    margin-top: 0;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.content-header h2 {
    font-size: 1.8rem; /* كان 1.6rem */
    color: var(--accent-color);
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-controls select {
    padding: 0.6rem; /* كان 0.5rem */
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-width: 160px; /* كان 150px */
    font-size: 1.1rem; /* زيادة حجم الخط */
}

/* خانات الفلترة الإضافية */
.filter-select {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.filter-select.hidden {
    display: none;
}

/* عرض الأفلام */
#movies-container {
    min-height: 200px;
}

.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
}

.list-view {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.movie-card {
    background-color: var(--card-bg-color);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* إضافة انتقال للظل */
    display: flex;
    flex-direction: column;
    min-height: 360px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* إضافة ظل خفيف */
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* زيادة الظل عند التحويم */
}

.movie-image-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    overflow: hidden;
}

.movie-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.movie-details {
    flex: 1;
    padding: 0.75rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    padding-bottom: 0; /* إزالة الهامش السفلي */
}

.movie-details h3 {
    font-size: 1.3rem; /* كان 1.2rem */
    margin-bottom: 0.3rem; /* تقليل الهامش السفلي */
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.3;
    min-height: auto;
}

.movie-site {
    font-size: 1.1rem; /* كان 1rem */
    color: #aaa;
    margin-top: 0; /* إزالة المسافة تحت اسم الفيلم */
    margin-bottom: 0; /* إزالة الهامش السفلي */
}

.movie-star {
    font-size: 1.1rem; /* كان 1rem */
    color: var(--accent-color);
    margin-top: 0.1rem; /* تقليل الهامش العلوي */
    margin-bottom: 0; /* إزالة الهامش السفلي */
    font-weight: 500;
}

.movie-bottom-controls {
    margin-top: 0; /* إزالة المسافة بين اسم الموقع والأيقونات */
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    background-color: rgba(0, 0, 0, 0.1);
}

.movie-top-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    z-index: 3;
}

.movie-number {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.movie-edit-controls {
    display: flex;
    gap: 0.5rem;
}

.movie-edit-btn, .movie-delete-btn {
    background-color: rgba(0, 0, 0, 0.6);
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.movie-edit-btn:hover, .movie-delete-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.movie-play-btn, .movie-favorite-btn, .movie-remove-btn {
    min-width: 38px; /* كان 36px */
    min-height: 38px; /* كان 36px */
    aspect-ratio: 1/1;
    z-index: 3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); /* إضافة ظل للأزرار */
}

.movie-play-btn, .movie-favorite-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.movie-play-btn:hover, .movie-favorite-btn:hover {
    background-color: var(--accent-hover);
}

.movie-favorite-btn {
    background-color: var(--success-color);
}

.movie-favorite-btn.marked {
    background-color: #FFD700;
}

.movie-remove-btn {
    background-color: var(--danger-color);
}

/* Pagination */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
}

.pagination-controls button {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-controls button:not(:disabled):hover {
    background-color: var(--accent-color);
}

.pagination-controls .page-number-btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
}

.pagination-controls .page-number-btn.current {
    background-color: var(--accent-color);
    color: white;
    font-weight: bold;
}

/* مودال عام */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease forwards;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--accent-color);
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.3s;
}

.close:hover {
    color: var(--accent-color);
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.modal button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.modal button:first-of-type {
    background-color: var(--accent-color);
    color: white;
}

.modal button:last-of-type {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.modal button:first-of-type:hover {
    background-color: var(--accent-hover);
}

.modal button:last-of-type:hover {
    background-color: var(--secondary-bg-color);
}

/* مودال تنظيف أسماء الأفلام */
#words-to-remove {
    width: 100%;
    padding: 10px;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
    direction: rtl;
    resize: vertical;
    font-family: 'Cairo', sans-serif;
    margin-top: 10px;
}

.words-input::placeholder {
    color: #777;
}

/* مودال تشغيل الفيلم */
#movie-player {
    width: 100%;
    height: 70vh;
    border: none;
}

/* قسم الإعدادات */
#password-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 2rem;
    align-items: center;
}

#password-input {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
}

#submit-password {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#submit-password:hover {
    background-color: var(--accent-hover);
}

.settings-tabs {
    display: flex;
    overflow-x: auto;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 1rem;
    background-color: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: background-color 0.3s;
    white-space: nowrap;
}

.tab-btn.active {
    background-color: var(--card-bg-color);
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.tab-content {
    display: none;
    padding: 1rem;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
}

/* Dropzone стиль для импорта */
.import-dropzone {
    border: 2px dashed var(--border-color);
    padding: 2rem;
    text-align: center;
    margin: 1rem 0;
    border-radius: 8px;
    transition: border-color 0.3s;
}

.import-dropzone.dragover {
    border-color: var(--accent-color);
}

.import-dropzone input[type="file"] {
    display: none;
}

.import-dropzone .btn {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s;
}

.import-dropzone .btn:hover {
    background-color: var(--accent-hover);
}

/* إدارة الأفلام */
.manage-movies-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

#movies-management-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    min-height: 200px;
}

.manage-movie-card {
    background-color: var(--card-bg-color);
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 300px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.manage-movie-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.manage-movie-details {
    padding: 0.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.manage-movie-details h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: auto; /* تم تغييرها من 2.4em إلى auto لإزالة المساحة الفارغة */
}

.manage-movie-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    margin-top: auto;
}

.manage-movie-actions button {
    flex: 1;
    min-width: 80px;
    padding: 0.5rem;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.manage-movie-edit {
    background-color: var(--accent-color);
    color: white;
}

.manage-movie-delete {
    background-color: var(--danger-color);
    color: white;
}

.site-actions {
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: 8px;
}

/* Результаты поиска */
.search-results {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 999;
    overflow-y: auto;
}

.search-results-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background-color: var(--modal-bg-color);
}

.close-search {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.3s;
}

.close-search:hover {
    color: var(--accent-color);
}

#search-results-container {
    padding: 1rem;
}

.search-result-item {
    display: flex;
    background-color: var(--card-bg-color);
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
}

.result-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.result-details {
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.result-details h3 {
    font-size: 1.1rem;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.result-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.result-actions button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.play-result-btn {
    background-color: var(--accent-color) !important;
}

.favorite-result-btn {
    background-color: var(--success-color) !important;
}

/* مؤشر التقدم المحسن */
.progress-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 400px;
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    z-index: 1100;
    text-align: center;
    transition: opacity 0.3s ease;
}

.progress-container.fade-out {
    opacity: 0;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-text {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
}

.cancel-import-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cancel-import-btn:hover {
    background-color: #d32f2f;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.progress-percentage {
    font-weight: bold;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background-color: var(--card-bg-color);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-color);
    border-radius: 5px;
    transition: width 0.3s ease;
}

.progress-info {
    font-size: 12px;
    color: #aaa;
    margin-top: 8px;
}

/* إضافة أنماط جديدة للعناصر المضافة سابقًا */
.highlight {
    animation: highlightAnimation 2s ease;
}

@keyframes highlightAnimation {
    0% { background-color: var(--card-bg-color); }
    50% { background-color: var(--accent-color); }
    100% { background-color: var(--card-bg-color); }
}

/* Helpers */
.hidden {
    display: none !important;
}

.danger-btn {
    background-color: var(--danger-color) !important;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.danger-btn:hover {
    opacity: 0.9;
}

.movie-open-setting {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.delete-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .search-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    #search-input {
        border-radius: 4px;
    }

    #search-app-btn {
        border-radius: 4px 4px 0 0;
    }

    #search-yandex-btn {
        border-radius: 0 0 4px 4px;
    }

    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .list-view .movie-card {
        flex-direction: column;
        height: auto;
    }

    .list-view .movie-image {
        width: 100%;
        height: 150px;
    }

    .movie-card h3 {
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
    }

    .settings-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: right;
        padding: 0.75rem;
    }

    #movies-management-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media screen and (min-width: 1200px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .movie-card h3 {
        font-size: 1.3rem; /* كان 1.1rem */
    }
}

@media screen and (min-width: 1600px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

/* Zoom Controls */
.zoom-controls {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: var(--secondary-bg-color);
    display: flex;
    flex-direction: row-reverse;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    z-index: 99;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border-radius: 50%;
    margin: 0 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.zoom-btn:hover {
    background-color: var(--accent-color);
}



